namespace AcademicPerformance.Models.Dtos;

/// <summary>
/// Temporary file data transfer object
/// </summary>
public class TemporaryFileDto
{
    public string TempId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public long SizeBytes { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public string? Description { get; set; }
    public string StorageType { get; set; } = string.Empty;
    public string? MinioBucketName { get; set; }
    public string? MinioObjectName { get; set; }
    public string? MinioETag { get; set; }
    public string? FileChecksum { get; set; }
    public string? PresignedUrl { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Note { get; set; }
}

/// <summary>
/// File download data transfer object
/// </summary>
public class FileDownloadDto
{
    public Stream FileStream { get; set; } = Stream.Null;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
}

/// <summary>
/// Presigned URL data transfer object
/// </summary>
public class PresignedUrlDto
{
    public string EvidenceFileId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string PresignedUrl { get; set; } = string.Empty;
    public int ExpirySeconds { get; set; }
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Evidence file metadata data transfer object
/// </summary>
public class EvidenceFileMetadataDto
{
    public string Id { get; set; } = string.Empty;
    public int AutoIncrementId { get; set; }
    public int AcademicSubmissionId { get; set; }
    public string? FormCriterionLinkId { get; set; }
    public string? SubmittedDynamicDataInstanceId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public long SizeBytes { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public string UploadedByUserId { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string StorageType { get; set; } = string.Empty;
    public string? MinioBucketName { get; set; }
    public string? MinioObjectName { get; set; }
    public string? MinioETag { get; set; }
    public string? FileChecksum { get; set; }
    public string? AccessUrl { get; set; }
    public DateTime? PresignedUrlExpiry { get; set; }
    public AcademicSubmissionDto? AcademicSubmission { get; set; }
}

/// <summary>
/// Academic submission data transfer object for file operations
/// </summary>
public class AcademicSubmissionDto
{
    public string Id { get; set; } = string.Empty;
    public int AutoIncrementId { get; set; }
    public string AcademicianUserId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Bulk upload result data transfer object
/// </summary>
public class BulkUploadResultDto
{
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int TotalFiles { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<FileUploadResultDto> Results { get; set; } = new();
}

/// <summary>
/// File upload result data transfer object
/// </summary>
public class FileUploadResultDto
{
    public string FileName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? EvidenceFileId { get; set; }
    public int? AutoIncrementId { get; set; }
    public long? SizeBytes { get; set; }
    public string? MinioObjectName { get; set; }
    public string? MinioETag { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// Bulk delete result data transfer object
/// </summary>
public class BulkDeleteResultDto
{
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int TotalFiles { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<FileDeleteResultDto> Results { get; set; } = new();
}

/// <summary>
/// File delete result data transfer object
/// </summary>
public class FileDeleteResultDto
{
    public string EvidenceFileId { get; set; } = string.Empty;
    public string? FileName { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Error { get; set; }
}
